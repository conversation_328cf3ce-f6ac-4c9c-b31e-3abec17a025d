import type { BaptismaRecordFilterInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useMemo } from 'react'
import { GET_BAPTISMA_RECORD_LIST } from '~/graphql/queries/get-baptisma-record-list'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetBaptismaRecord(enabled: boolean) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1).withOptions({ history: 'push' }))

  // URL state for all filter fields
  const [registrationNo, setRegistrationNo] = useQueryState('registration_no', parseAsString.withDefault(''))
  const [hming, setHming] = useQueryState('hming', parseAsString.withDefault(''))
  const [paHming, setPaHming] = useQueryState('pa_hming', parseAsString.withDefault(''))
  const [nuHming, setNuHming] = useQueryState('nu_hming', parseAsString.withDefault(''))
  const [khua, setKhua] = useQueryState('khua', parseAsString.withDefault(''))
  const [pianNi, setPianNi] = useQueryState('pian_ni', parseAsString.withDefault(''))
  const [baptismaChanNi, setBaptismaChanNi] = useQueryState('baptisma_chan_ni', parseAsString.withDefault(''))
  const [chantirtu, setChantirtu] = useQueryState('chantirtu', parseAsString.withDefault(''))

  // Create filter object from URL state
  const activeBaptismaFilter = useMemo(() => {
    const hasAnyValue = registrationNo || hming || paHming || nuHming || khua || pianNi || baptismaChanNi || chantirtu

    if (!hasAnyValue) {
      return null
    }

    const filter: BaptismaRecordFilterInput = {}
    if (registrationNo)
      filter.registration_no = registrationNo
    if (hming)
      filter.hming = hming
    if (paHming)
      filter.pa_hming = paHming
    if (nuHming)
      filter.nu_hming = nuHming
    if (khua)
      filter.khua = khua
    if (pianNi)
      filter.pian_ni = pianNi
    if (baptismaChanNi)
      filter.baptisma_chan_ni = baptismaChanNi
    if (chantirtu)
      filter.chantirtu = chantirtu

    return filter
  }, [registrationNo, hming, paHming, nuHming, khua, pianNi, baptismaChanNi, chantirtu])

  const searchBaptisma = (_data: BaptismaRecordFilterInput | null) => {
    setPage(1) // Reset to first page on new search
    // The filter state is now managed by URL parameters, so this function
    // is kept for compatibility but the actual filtering happens through URL state
  }

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-baptisma-record', page, activeBaptismaFilter],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_BAPTISMA_RECORD_LIST,
        variables: {
          first: 20,
          page,
          baptisma_filter: activeBaptismaFilter,
        },
      })
    },
    enabled,
  })

  const lastPage = data?.getBaptismaRecordList?.paginator_info?.last_page ?? 1

  return {
    data,
    isLoading,
    isError,
    page,
    handlePage,
    lastPage,
    searchBaptisma,
    // URL state values
    registrationNo,
    hming,
    paHming,
    nuHming,
    khua,
    pianNi,
    baptismaChanNi,
    chantirtu,
    // URL state setters
    setRegistrationNo,
    setHming,
    setPaHming,
    setNuHming,
    setKhua,
    setPianNi,
    setBaptismaChanNi,
    setChantirtu,
  }
}
