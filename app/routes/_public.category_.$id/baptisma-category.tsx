import type { BaptismaRecordFilterInput } from '~/gql/graphql'
import { SearchIcon, X } from 'lucide-react'
import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs'
import { Accordion, AccordionContent, AccordionItem } from '~/components/ui/accordion'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'

interface Props {
  searchBaptisma: (data: BaptismaRecordFilterInput | null) => void
  isLoading: boolean
}

export default function BaptismaCategory({ searchBaptisma, isLoading }: Props) {
  // URL state for search accordion
  const [isSearchOpen, setIsSearchOpen] = useQueryState('search_open', parseAsBoolean.withDefault(false))

  // URL state for form fields
  const [registrationNo, setRegistrationNo] = useQueryState('registration_no', parseAsString.withDefault(''))
  const [hming, setHming] = useQueryState('hming', parseAsString.withDefault(''))
  const [paHming, setPaHming] = useQueryState('pa_hming', parseAsString.withDefault(''))
  const [nuHming, setNuHming] = useQueryState('nu_hming', parseAsString.withDefault(''))
  const [khua, setKhua] = useQueryState('khua', parseAsString.withDefault(''))
  const [pianNi, setPianNi] = useQueryState('pian_ni', parseAsString.withDefault(''))
  const [baptismaChanNi, setBaptismaChanNi] = useQueryState('baptisma_chan_ni', parseAsString.withDefault(''))
  const [chantirtu, setChantirtu] = useQueryState('chantirtu', parseAsString.withDefault(''))

  const form = useAppForm({
    defaultValues: {
      registration_no: registrationNo,
      hming,
      pa_hming: paHming,
      nu_hming: nuHming,
      khua,
      pian_ni: pianNi,
      baptisma_chan_ni: baptismaChanNi,
      chantirtu,
    },
    onSubmit: async ({ value }) => {
      // Update URL state with form values
      setRegistrationNo(value.registration_no || '')
      setHming(value.hming || '')
      setPaHming(value.pa_hming || '')
      setNuHming(value.nu_hming || '')
      setKhua(value.khua || '')
      setPianNi(value.pian_ni || '')
      setBaptismaChanNi(value.baptisma_chan_ni || '')
      setChantirtu(value.chantirtu || '')

      // return null if all fields are empty
      const isEmpty = Object.values(value).every(val => !val || val === '')
      searchBaptisma(isEmpty ? null : value)
    },
  })

  // Trigger search when URL parameters change (on page load or navigation)
  // useEffect(() => {
  //   const urlValues = {
  //     registration_no: registrationNo,
  //     hming,
  //     pa_hming: paHming,
  //     nu_hming: nuHming,
  //     khua,
  //     pian_ni: pianNi,
  //     baptisma_chan_ni: baptismaChanNi,
  //     chantirtu,
  //   }
  //
  //   // Check if any URL parameters have values
  //   const hasUrlValues = Object.values(urlValues).some(val => val !== '')
  //
  //   if (hasUrlValues) {
  //     // If there are URL values, trigger search and open accordion
  //     setIsSearchOpen(true)
  //     searchBaptisma(urlValues)
  //   }
  // }, [registrationNo, hming, paHming, nuHming, khua, pianNi, baptismaChanNi, chantirtu])

  return (
    <>
      <Card className="my-4 bg-white">
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <Breadcrumb className="pt-4 pb-4">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator>
                    /
                  </BreadcrumbSeparator>
                  <BreadcrumbItem>
                    Baptisma Records
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            {isSearchOpen
              ? (
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => setIsSearchOpen(false)}
                  >
                    <X className="text-xl" />
                  </Button>
                )
              : (
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => setIsSearchOpen(true)}
                  >
                    <SearchIcon className="text-xl" />
                  </Button>
                )}
          </div>
          <Accordion
            type="single"
            value={isSearchOpen ? 'search-form' : ''}
            className="border-none"
          >
            <AccordionItem value="search-form" className="border-none">
              <AccordionContent>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    form.handleSubmit()
                  }}
                  className="grid grid-cols-4 gap-4 pt-4"
                >
                  <form.AppField
                    name="registration_no"
                    children={field => <field.InputField label="Registration No" />}
                  />
                  <form.AppField
                    name="hming"
                    children={field => <field.InputField label="Hming" />}
                  />
                  <form.AppField
                    name="pa_hming"
                    children={field => <field.InputField label="Pa hming" />}
                  />
                  <form.AppField
                    name="nu_hming"
                    children={field => <field.InputField label="Nu hming" />}
                  />

                  <form.AppField
                    name="khua"
                    children={field => <field.InputField label="Khua" />}
                  />
                  <form.AppField
                    name="pian_ni"
                    children={field => <field.InputField label="Pian ni" type="date" />}
                  />
                  <form.AppField
                    name="baptisma_chan_ni"
                    children={field => <field.InputField label="Baptisma chan ni" type="date" />}
                  />
                  <form.AppField
                    name="chantirtu"
                    children={field => <field.InputField label="Chantirtu" />}
                  />
                  <div className="col-span-1">
                    <Button type="submit" isLoading={isLoading}>
                      Search
                    </Button>
                  </div>
                </form>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </>
  )
}
