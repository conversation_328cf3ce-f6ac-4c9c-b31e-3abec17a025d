import { useParams } from 'react-router'
import CommonError from '~/components/common/common-error'
import PagePagination from '~/components/common/page-pagination'
import useGetBaptismaRecord from '~/hooks/use-get-baptisma-record'
import useGetInneihRecord from '~/hooks/use-get-inneih-record'
import { BAPTISMA_CATEGORY_ID, INNEIH_CATEGORY_ID } from '~/lib/constants'
import BaptismaCategory from './baptisma-category'
import BaptismaTable from './baptisma-table'
import InneihCategory from './inneih-category'
import InneihTable from './inneih-table'
import OthersCategory from './others-category'
import OthersTable from './others-table'
import useGetDocumentsByCategoryId from './use-get-documents-by-category-id'

export default function CategoryById() {
  const { id } = useParams()

  // Call all hooks unconditionally at the top level
  const baptismaData = useGetBaptismaRecord(id === BAPTISMA_CATEGORY_ID)
  const othersData = useGetDocumentsByCategoryId({ id })
  const inneihData = useGetInneihRecord(id === INNEIH_CATEGORY_ID)

  if (id === BAPTISMA_CATEGORY_ID) {
    const {
      data,
      lastPage,
      handlePage,
      page,
      searchBaptisma,
      isLoading,
      isError,
      registrationNo,
      hming,
      paHming,
      nuHming,
      khua,
      pianNi,
      baptismaChanNi,
      chantirtu,
      setRegistrationNo,
      setHming,
      setPaHming,
      setNuHming,
      setKhua,
      setPianNi,
      setBaptismaChanNi,
      setChantirtu,
    } = baptismaData

    return (
      <>
        <BaptismaCategory
          searchBaptisma={searchBaptisma}
          isLoading={isLoading}
          registrationNo={registrationNo}
          hming={hming}
          paHming={paHming}
          nuHming={nuHming}
          khua={khua}
          pianNi={pianNi}
          baptismaChanNi={baptismaChanNi}
          chantirtu={chantirtu}
          setRegistrationNo={setRegistrationNo}
          setHming={setHming}
          setPaHming={setPaHming}
          setNuHming={setNuHming}
          setKhua={setKhua}
          setPianNi={setPianNi}
          setBaptismaChanNi={setBaptismaChanNi}
          setChantirtu={setChantirtu}
        />
        {isError && <CommonError message="Unable to get document list" />}
        <BaptismaTable documents={data?.getBaptismaRecordList?.data ?? []} />
        {lastPage > 1 && (
          <div className="mb-4">
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePage}
              lastPage={lastPage}
            />
          </div>
        )}
      </>
    )
  }

  if (id === INNEIH_CATEGORY_ID) {
    // Use the general documents hook which already supports Inneih filtering
    const { data, lastPage, handlePage, page, isLoading, searchInneih, isError } = inneihData

    return (
      <>
        <InneihCategory searchInneih={searchInneih} isLoading={isLoading} />
        {isError && <CommonError message="Unable to get document list" />}
        <InneihTable documents={data?.getInneihRecordList?.data || []} />
        {lastPage > 1 && (
          <div className="mb-4">
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePage}
              lastPage={lastPage}
            />
          </div>
        )}
      </>
    )
  }

  // Default case for other categories
  const { data, lastPage, handlePage, page, isLoading, searchOthers } = othersData

  return (
    <>
      <OthersCategory id={id!} searchOthers={searchOthers} isLoading={isLoading} />
      <OthersTable documents={data?.getDocumentsByCategoryId?.data || []} />
      {lastPage > 1 && (
        <div className="mb-4">
          <PagePagination
            currentPage={page}
            handlePagePagination={handlePage}
            lastPage={lastPage}
          />
        </div>
      )}
    </>
  )
}
